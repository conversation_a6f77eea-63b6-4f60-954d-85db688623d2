"use client";

import { useCallback, useEffect, useState } from "react";
import { CustomContainer } from "../Common/Custom-Display";
import useApiRequest from "../helper/hook/useApiRequest";
import authStorage from "@/utils/API/AuthStorage";
import {
  deleteCommunity,
  getOneCommunity,
  joinCommunity,
  reportCommunity,
  reportProject,
} from "@/app/action";
import SingleCommunitySkeleton from "../Loader/SingleCommunitySkeleton";
import Link from "next/link";
import {
  EditPencilIcon,
  LeftArrowBackIcon,
  LocationIcon,
  ShareIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";
import ShowMenuList from "../Common/ShowMenuList";
import { blurDataURL } from "@/utils/function";
import AboutModal from "../ProjectPage/SingleProject/AboutModal";
import Image from "next/image";
import { ChevronRight, Flag } from "lucide-react";
import toast from "react-hot-toast";
import CustomButton from "../Common/Custom-Button";
import CommunityPost from "./CommunityPost";
import CommunitiesModal from "./CommunitiesModal";
import PopUpModal from "../Common/PopUpModal";
import { usePathname } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import PostModal from "../HomePage/Form/PostModal";
import FollowingAndFollowersModal from "../Profile/FollowingAndFollowersModal";
import Empty from "../Common/Empty";
import ShareModal from "../Common/ShareModal";
import ReportModal from "../Common/ReportModal";
import { safeToast } from "@/utils/safeToast";
import tempImg from "../../../public/images/assets/group-community.jpg";
import UserAvatar from "../Common/UserAvatar";

const SingleCommunity = ({ slug, origin }) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [communityData, setCommunityData] = useState(null);
  const [userData, setUserData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAboutModalOpen, setIsAboutModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [deleteData, setDeleteData] = useState(null);
  const [refresh, setRefresh] = useState(false);
  const [isJoined, setIsJoined] = useState(false);
  const [type, setType] = useState({
    isOpen: false,
    type: null,
  });
  const [openModal, setOpenModal] = useState({
    isOpen: false,
    type: null,
  });
  const api = useApiRequest();
  const router = useRouter();
  const [isShareOpen, setIsShareOpen] = useState(false);
  const [shareData, setShareData] = useState(null);
  const [isReported, setIsReported] = useState(false);
  const [reportData, setReportData] = useState(null);

  const joinHandler = async (id) => {
    setIsJoined((prev) => !prev);
    api.sendRequest(
      joinCommunity,
      (res) => {
        //   console.log(res);
        safeToast.success(res?.message);
      },
      {
        id,
      }
    );
  };

  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  const otherProjectSetting = [
    {
      label: "Share",
      className: "",
      icon: <ShareIcon size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        setIsShareOpen(true);
        setShareData(ele);
      },
    },
    {
      label: "Report this Community",
      className: "",
      icon: <Flag size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        setIsReported(true);
        setReportData(ele);
      },
    },
  ];

  const fetchData = useCallback(() => {
    setIsLoading(true);
    api.sendRequest(
      getOneCommunity,
      (res) => {
        const currentUserData = authStorage.getProfileDetails();
        setUserData(currentUserData);
        if (res?.data) {
          setCommunityData({
            ...res?.data,
            createdBy:
              +currentUserData?.id === +res?.data?.UserId ? "me" : "User",
          });
          setIsJoined(Boolean(+res?.data?.isJoined));
        }
        setIsLoading(false);
        //   setIsFollow(Boolean(+res?.data?.isFollowed));
      },
      slug,

      "",
      () => {
        setIsLoading(false);
      }
    );
  }, [refresh]);

  useEffect(() => {
    fetchData();
  }, [refresh]);
  return (
    <>
      {/* <ReportModal
        title="Why are you reporting this community?"
        isOpen={isReported}
        setIsOpen={setIsReported}
        onConfirm={(value) => {}}
      /> */}
      <ReportModal
        title="Why are you reporting this community?"
        isOpen={isReported}
        setIsOpen={setIsReported}
        isLoading={api?.isLoading}
        onConfirm={(reason) => {
          const payload = {
            reason,
            id: reportData?.id,
          };
          api.sendRequest(
            reportCommunity,
            (res) => {
              safeToast.success(res?.message);
              setIsReported(false);
              setRefresh((prev) => !prev);
              setReportData(null);
            },
            payload
          );
        }}
      />
      {/* Share Modal */}
      <ShareModal
        open={isShareOpen}
        onClose={() => {
          setIsShareOpen(false);
        }}
        shareUrl={`${origin}/communities/${shareData?.slug}`}
        title={`${shareData?.name} \nCheckout this community:`}
      />
      {/* Edit Community */}
      <CommunitiesModal
        setRefresh={setRefresh}
        editData={editData}
        setEditData={setEditData}
        modalTitle={"Edit Community"}
        modalSubmitButton={"Update"}
      />
      {/* Delete Confirm Popup */}
      <PopUpModal
        isLoading={api.isLoading}
        isOpen={deleteData}
        setIsOpen={setDeleteData}
        mainMessage="Delete Community"
        subMessage="Are you sure you want to Delete Community Permanently?"
        onConfirm={() => {
          api.sendRequest(
            deleteCommunity,
            () => {
              router.push("/communities");
            },
            deleteData?.id,

            "Community Deleted Successfully"
          );
        }}
      />

      {/* Add Post Modal */}
      <PostModal
        reFetchData={resetDataList}
        setRefresh={setRefresh}
        open={openModal}
        setOpen={setOpenModal}
        showPostVisibility={false}
        showProject={false}
        showSaveAsDraft={false}
        title={"Create Post for Community"}
        communityData={communityData}
      />

      {/* Community Member List */}
      <FollowingAndFollowersModal
        // setRefresh={setRefresh}
        modalType={type}
        setModalType={setType}
        communityId={communityData?.id}
        loginUserData={userData}
      />

      {/* Community Details */}
      <CustomContainer className="tw-py-4 tw-h-full">
        {isLoading ? (
          <SingleCommunitySkeleton />
        ) : communityData ? (
          <>
            <AboutModal
              isOpen={isAboutModalOpen}
              setIsOpen={setIsAboutModalOpen}
              projectData={communityData}
              loginUser={userData}
              extraText={
                (+communityData?.members ?? 0) === 0
                  ? "No one has joined this community yet"
                  : null
              }
            />
            {/* <Link href={revertRoute} className="tw-mb-4 tw-block">
              <LeftArrowBackIcon />
            </Link> */}
            <button
              type="button"
              onClick={() => {
                router.back();
              }}
              className="tw-mb-4"
            >
              <LeftArrowBackIcon />
            </button>
            {/* Community Bg Image */}
            {
              <div className="tw-relative tw-w-full tw-h-[21rem] ">
                <Image
                  src={communityData?.image ?? tempImg}
                  alt={"communities logo"}
                  fill
                  className="tw-rounded-3xl tw-object-cover"
                  placeholder="blur"
                  priority
                  blurDataURL={blurDataURL(800, 400)}
                />
                <div className="tw-absolute tw-inset-0 tw-rounded-3xl tw-bg-projectCoverImgBg" />
                {communityData?.createdBy === "me" ? (
                  <div className="tw-flex tw-z-40 tw-gap-3 tw-items-center tw-absolute tw-right-8 tw-top-5">
                    <button
                      className="tw-bg-transparent tw-border tw-border-white tw-text-white tw-font-semibold tw-rounded-full tw-px-5 tw-py-2"
                      type="button"
                      onClick={() => {
                        setEditData(communityData);
                      }}
                    >
                      Edit
                    </button>
                    <button
                      className="tw-font-semibold tw-text-[#EF3B41]"
                      type="button"
                      onClick={() => {
                        setDeleteData(communityData);
                      }}
                    >
                      Delete
                    </button>
                  </div>
                ) : (
                  <>
                    <ShowMenuList
                      data={communityData}
                      menuList={otherProjectSetting}
                    >
                      <div className="tw-cursor-pointer tw-flex tw-z-40 tw-gap-3 tw-items-center tw-absolute tw-right-7 tw-top-7">
                        <ThreeDotMenuIcon fill="#fff" size={24} />
                      </div>
                    </ShowMenuList>
                  </>
                )}
              </div>
            }
            {/* Title */}

            <div className="lg:tw-flex  lg:tw-flex-row tw-justify-between lg:tw-items-center tw-mt-4">
              <button
                onClick={() => setIsAboutModalOpen((prev) => !prev)}
                className="tw-flex tw-gap-2 tw-items-center tw-max-w-[30rem] lg:tw-max-w-[42rem]"
              >
                <h1 className="tw-my-5 tw-text-2xl md:tw-text-4xl tw-text-left tw-font-bold tw-text-primary-black ">
                  {communityData?.name}
                </h1>
                <ChevronRight size={30} />
              </button>
              {userData?.id !== communityData?.User?.id ? (
                <div className="tw-flex tw-justify-center tw-items-center lg:tw-block tw-mb-2 lg:tw-my-0">
                  <CustomButton
                    type="button"
                    count={8}
                    onClick={() => {
                      joinHandler(communityData?.id);
                    }}
                    className={`${
                      isJoined
                        ? "!tw-bg-transparent !tw-border !tw-border-[#787E89] !tw-text-[#787E89]"
                        : "!tw-bg-primary-purple !tw-text-white"
                    }  !tw-text-lg !tw-py-4 !tw-px-10 !tw-rounded-full !tw-font-semibold !tw-mx-0`}
                  >
                    {isJoined ? "Joined" : "Join"}
                  </CustomButton>
                </div>
              ) : (
                <>
                  <p className="tw-italic tw-text-[#787E89] ">Created by me</p>
                </>
              )}
            </div>
            {communityData?.location && (
              <div className="tw-text-[#787E89] tw-flex tw-gap-1.5 tw-text-sm lg:tw-text-base tw-items-start lg:tw-items-center">
                <div className="tw-relative tw-top-1 md:tw-top-0">
                  <LocationIcon />
                </div>
                <p className="tw-max-w-[22rem] md:tw-max-w-[35rem] lg:tw-max-w-[100%]">
                  {communityData?.location}
                </p>
              </div>
            )}

            {/* Members */}
            <div className="tw-flex tw-items-center tw-gap-5 tw-mt-4">
              <div className="tw-flex tw-items-center  tw-space-x-[-7px] ">
                {communityData?.recentMembers?.map((data, index) => (
                  <div key={`${data?.User?.id}-${index}`}>
                    <UserAvatar
                      imageParentClassName="!tw-w-10 !tw-h-10 !tw-border-[1.5px] !tw-border-white"
                      userNameClassName="!tw-text-base !tw-font-normal"
                      imageUrl={data?.User?.image}
                      userName={data?.User?.firstName}
                    />
                  </div>
                ))}
              </div>
              <div>
                <p className="tw-text-sm tw-text-primary-black">
                  {communityData?.members ?? 0} members
                </p>
                {communityData?.members && +communityData?.members !== 0 && (
                  <button
                    type="button"
                    onClick={() => {
                      setType({
                        isOpen: true,
                        type: "User",
                      });
                      // setType("communityMember");
                    }}
                    className="tw-font-semibold tw-leading-[110%] tw-text-primary-purple "
                  >
                    See All
                  </button>
                )}
              </div>
            </div>

            {/* Posts */}
            <div className="tw-flex tw-justify-between tw-items-center tw-my-5">
              <div>
                <p className="tw-text-primary-black tw-font-bold tw-text-2xl">
                  Posts
                </p>
              </div>
              {(communityData?.createdBy === "me" || isJoined) && (
                <button
                  type="button"
                  onClick={() => {
                    setOpenModal({
                      isOpen: true,
                      type: "post",
                    });
                  }}
                  className="tw-fixed tw-bottom-20 tw-right-6 lg:tw-static tw-bg-primary-purple tw-border-primary-purple  tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white !tw-px-4 !tw-py-4 tw-transition tw-ease-in-out tw-duration-300 hover:tw-scale-110 !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold"
                >
                  <EditPencilIcon className="tw-h-[18px] tw-w-[18px] tw-font-semibold" />
                  <span className="tw-hidden lg:tw-inline">Create</span>
                </button>
              )}
            </div>
            <CommunityPost
              CommunityId={communityData?.id}
              userData={userData}
              userId={communityData?.UserId}
              dataList={dataList}
              setDataList={setDataList}
              pagination={pagination}
              setPagination={setPagination}
              resetDataList={resetDataList}
              origin={origin}
            />
          </>
        ) : (
          <>
            <div className="tw-mt-10">
              <Empty />
            </div>
          </>
        )}
      </CustomContainer>
    </>
  );
};

export default SingleCommunity;
