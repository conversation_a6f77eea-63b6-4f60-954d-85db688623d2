import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import Stories, { WithSeeMore } from "react-insta-stories";
import { Eye, MoreVertical, Pencil, PenLine, Trash2 } from "lucide-react";
import {
  CloseModalIcon,
  HeartFillOutIcon,
  HeartIcon,
  StoryLikeIcon,
} from "@/utils/icons";
import VideoPlayer from "./VideoPlayer/VideoPlayer";
import HLSPlayer from "./VideoPlayer/HLSPlayer";
import dayjs from "dayjs";
import {
  createProxyImageUrl,
  fonts,
  formatNumber,
  generateAvatarCanvas,
  getTimePassedFromNow,
  parseColor,
} from "@/utils/function";
import { fontMap } from "./Font";
import NextImage from "next/image";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "nextjs-toploader/app";

import { Suspense, use, useCallback, useContext, useEffect } from "react";
import { useState } from "react";
import PopUpModal from "../Common/PopUpModal";
import useApiRequest from "../helper/hook/useApiRequest";
import {
  deleteHighlights,
  getStoryLikeAndViewCount,
  getLikedStoryUser,
  likeStory,
  updateHighlights,
} from "@/app/action";
import { safeToast } from "@/utils/safeToast";
import AddHighlightModal from "../Profile/AddHighlightModal";
import AnimatedAlignPositioned from "./AnimatedAlignPositioned";
import { valContext } from "../context/ValContext";
import authStorage from "@/utils/API/AuthStorage";
import UserAvatar from "../Common/UserAvatar";
import SelectProjectSkeleton from "../Loader/SelectProjectSkeleton";
import InfiniteScroll from "react-infinite-scroll-component";

const RenderProperties = ({ properties, action, router }) => {
  /* Render all properties */
  return properties?.map((prop, index) => {
    const dx = prop?.position?.dx ?? 0;
    const dy = prop?.position?.dy ?? 0;
    const backGroundColor = prop?.backGroundColor ?? "transparent";
    const text = prop?.text ?? "";
    const fontSize = prop?.fontSize ?? 16;
    const textColor = prop?.textColor ?? "#000";
    const scale = prop?.scale ?? 1;
    const rotation = prop?.rotation ?? 0;
    const fontFamily = prop?.fontFamily ?? "default";
    const slug = prop?.slug;
    const textAlignPosition = {
      0: "center",
      1: "left",
      2: "right",
    };

    const selectedFont = fonts[fontFamily];
    const fontClass = fontMap[selectedFont]?.className ?? "";

    return (
      <div className="tw-w-full tw-h-full tw-absolute " key={index}>
        <AnimatedAlignPositioned
          dx={dx}
          dy={dy}
          alignment={textAlignPosition[0]}
          rotateDegrees={rotation}
          duration={0}
          curve="easeInOut"
        >
          <Popover
            key={index}
            onOpenChange={(open) => {
              if (open) {
                action("pause");
              } else {
                action("play");
              }
            }}
          >
            <PopoverTrigger asChild>
              <div
                className={fontClass}
                style={{
                  // position: "absolute",
                  // top: `calc(55% + ${dy * 50}%)`, // convert -1..1 to percent offset from center
                  // left: `calc(55% + ${dx * 50}%)`,
                  // transform: `translate(-50%, -50%) scale(${scale}) rotate(${rotation}rad)`,
                  transform: `scale(${scale}) rotate(${rotation}rad)`,
                  color: parseColor(textColor),
                  fontSize: `${fontSize / 1.25}px`,
                  fontWeight: "500",
                  padding: "0.6rem",
                  borderRadius: "10px",
                  backgroundColor: parseColor(backGroundColor),
                  cursor: "pointer",
                  pointerEvents: "auto",
                  zIndex: 999999 + index, // Ensure each element has unique z-index
                  // maxWidth: "80%",
                  wordWrap: "break-word",
                  // width: "100%",
                  textAlign: textAlignPosition[prop?.textAlign ?? 0],
                }}
              >
                {text}
              </div>
            </PopoverTrigger>
            <PopoverContent
              side="top"
              align="center"
              className="tw-max-w-xs tw-p-4 tw-text-center "
            >
              <p
                onClick={() => {
                  if (slug) {
                    router.push(`/projects/${slug}`);
                  }
                }}
              >
                {text}
              </p>
            </PopoverContent>
          </Popover>
        </AnimatedAlignPositioned>
      </div>
    );
  });
};
// Custom Header Component with 3-dot settings
const CustomStoryHeader = ({
  profileImage,
  heading,
  subheading,
  currentUser,
  onAction,
  ele,
  action,
  setCurrentAction,
  isSettingVisible = false,
  router,
}) => {
  const settings = [
    {
      label: "Edit Highlight",
      icon: <Pencil stroke="#2D394A" size={17} />,
      onClick: () => {
        action("pause");
        setCurrentAction(action);
        onAction("edit", currentUser);
      },
    },
    {
      label: "Remove Story",
      className: "tw-text-[#EF3B41] hover:tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        // console.log(ele, "here");
        onAction("delete", ele);
      },
    },
  ];

  return (
    <div className="tw-inline-block">
      <div
        onClick={() => {
          if (currentUser?.slug) {
            action("pause");
            router?.push(`/user/${currentUser?.slug}`);
          }
        }}
        className={`tw-absolute ${
          currentUser?.slug && "tw-cursor-pointer"
        } tw-top-2 tw-left-0  tw-flex tw-items-center tw-gap-2.5 tw-p-4 tw-z-[9999] tw-w-full`}
      >
        <div className="tw-relative tw-w-12 tw-h-12 tw-rounded-full">
          <NextImage
            src={profileImage}
            alt="User"
            className="!tw-rounded-full !tw-object-cover tw-border-2 tw-border-white"
            fill
          />
        </div>
        <div className="tw-flex-1">
          <p className="tw-text-[#ffffffe6] tw-font-medium">{heading}</p>
          <p className="tw-text-[#fffc] tw-text-[.6rem]">{subheading}</p>
        </div>

        {isSettingVisible && (
          <DropdownMenu
            onOpenChange={(open) => {
              if (open) {
                // Pause story when dropdown opens
                setCurrentAction(action);
                action("pause");
                // console.log("Dropdown opened - story paused");
              } else {
                // Resume story when dropdown closes (optional)
                // console.log("Dropdown clo/sed");
              }
            }}
          >
            <DropdownMenuTrigger asChild>
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  // console.log("Dropdown trigger clicked");
                }}
                className="tw-text-white tw-p-2 hover:tw-bg-white/20 tw-rounded-full tw-transition-colors tw-cursor-pointer"
              >
                <MoreVertical size={20} />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="tw-z-[99999] tw-bg-white tw-border tw-border-gray-200 tw-rounded-md tw-shadow-lg tw-min-w-[160px]"
              align="end"
              side="bottom"
              sideOffset={8}
            >
              {settings.map((setting, index) => (
                <DropdownMenuItem
                  key={index}
                  className={`tw-flex tw-items-center tw-gap-3 tw-px-3 tw-py-2.5 tw-cursor-pointer tw-text-sm hover:tw-bg-gray-50 focus:tw-bg-gray-50 tw-transition-colors ${
                    setting.className || ""
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setting.onClick(ele);
                  }}
                >
                  <span className="tw-flex tw-items-center tw-justify-center tw-w-4 tw-h-4">
                    {setting.icon}
                  </span>
                  <span className="tw-flex-1">{setting.label}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );
};

const ModifyStoryPreview = ({
  isOpen,
  setIsOpen,
  currentUserIndex,
  setCurrentUserIndex,
  storyData,
  dataKey = "Stories",
  setReload = () => {},
  isSettingVisible = false,
  isHighlight = false,
  loginUserData,
}) => {
  const router = useRouter();
  const [deleteData, setDeleteData] = useState(null);
  const [isPaused, setIsPaused] = useState(false);
  const [currentAction, setCurrentAction] = useState(null);
  const [stories, setStories] = useState([]);
  const [isHighlighModalOpen, setIsHighlighModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [userList, setUserList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 1,
    total: 0,
  });

  const resetDataList = () => {
    setUserList([]);
    setPagination({
      page: 1,
      limit: 10,
      total: 0,
    });
  };

  console.log(userList);

  // const { loginUserData } = useContext(valContext);
  const api = useApiRequest(false);
  // API instance to fetch user list
  const api2 = useApiRequest();

  const rebuildStories = useCallback(
    (story, user, action) => {
      setStories((prev) =>
        prev.map((prevStories) => {
          let storyData = {
            ...prevStories,
          };

          if (storyData?.storyId === story?.id) {
            storyData = {
              ...storyData,
              seeMore: ({ close }) => (
                <>
                  <SeeMoreDrawer
                    story={story}
                    close={close}
                    storyCount={storyData?.storyCount ?? {}}
                    isLikedByCurrentUser={story?.isLikedByCurrentUser}
                    updateStoryLike={updateStoryLike}
                    getStoryLikeUserList={getStoryLikeUserList}
                    userList={user}
                    resetDataList={resetDataList}
                    isLoading={false}
                    pagination={pagination}
                    setPagination={setPagination}
                    action={action}
                  />
                </>
              ),
            };
          }

          return storyData;
        })
      );
      action("pause");
    },
    [userList]
  );

  const getStoryLikeUserList = (story, action, page = null) => {
    const queryParams = {
      storyId: story?.id,
      page: page ?? pagination.page,
      limit: pagination.limit,
      sortBy: "isLike",
    };
    api2.sendRequest(
      getLikedStoryUser,
      (res) => {
        const user =
          userList?.length > 0
            ? [...userList, ...res?.data?.rows]
            : [...res?.data?.rows];
        setUserList(user);
        rebuildStories(story, user, action);
        setPagination((prev) => ({
          ...prev,
          total: res?.data?.count,
        }));
      },
      queryParams
    );
  };

  // Handler for settings actions
  const handleHeaderAction = (type, data) => {
    setIsPaused(true);
    switch (type) {
      case "delete":
        // console.log("Muting user:", data);
        setDeleteData({
          ...data,
        });
        break;
      case "edit":
        setIsHighlighModalOpen(true);
        setEditData(data);
        break;

      default:
      // console.log("Unknown action:", type);
    }
  };

  // Preload images for faster rendering
  useEffect(() => {
    if (!storyData || storyData.length === 0) return;

    const loadAllImage = (imageArray) => {
      imageArray?.forEach((story) => {
        if (story?.Story?.overlayImage ?? story?.overlayImage) {
          const img = new Image();
          img.src = story?.Story?.overlayImage ?? story?.overlayImage;
        }
        if (
          (story?.Story?.mediaLink && story?.Story?.mediaType === "image") ||
          (story?.mediaLink && story?.mediaType === "image")
        ) {
          const img = new Image();
          img.src = story?.Story?.mediaLink ?? story.mediaLink;
        }
      });
    };

    // console.log("Preload images");
    const preloadImages = () => {
      // Preload current user's images
      const currentUser = storyData[currentUserIndex];
      //   currentUser[dataKey]?.forEach((story) => {
      //     if (story?.Story?.overlayImage ?? story?.overlayImage) {
      //       const img = new Image();
      //       img.src = story?.Story?.overlayImage ?? story?.overlayImage;
      //     }
      //     if (
      //       (story?.Story?.mediaLink && story?.Story?.mediaType === "image") ||
      //       (story?.mediaLink && story?.mediaType === "image")
      //     ) {
      //       const img = new Image();
      //       img.src = story?.Story?.mediaLink ?? story.mediaLink;
      //     }
      //   });
      loadAllImage(currentUser[dataKey]);
      // Preload next user's first image
      if (currentUserIndex < storyData.length - 1) {
        const nextUser = storyData[currentUserIndex + 1];
        const firstStory = nextUser[dataKey]?.[0];
        // console.log(firstStory, "Next User Story", nextUser);
        loadAllImage(nextUser[dataKey]);
        // if (firstStory?.overlayImage || firstStory?.Story?.overlayImage) {
        //   const img = new Image();
        //   img.src = firstStory?.Story?.overlayImage ?? firstStory.overlayImage;
        // }
        // if (
        //   (firstStory?.Story?.mediaLink &&
        //     firstStory?.Story?.mediaType === "image") ||
        //   (firstStory?.mediaLink && firstStory?.mediaType === "image")
        // ) {
        //   const img = new Image();
        //   img.src = firstStory?.Story?.mediaLink ?? firstStory.mediaLink;
        // }
      }

      // Preload previous user's last image
      if (currentUserIndex > 0) {
        const prevUser = storyData[currentUserIndex - 1];
        const lastStory = prevUser[dataKey]?.[prevUser[dataKey].length - 1];
        loadAllImage(prevUser[dataKey]);
        // console.log(lastStory, "last User Story", prevUser);
        // if (lastStory?.overlayImage ?? lastStory?.Story?.overlayImage) {
        //   const img = new Image();
        //   img.src = lastStory?.Story?.overlayImage ?? lastStory.overlayImage;
        // }
        // if (
        //   (lastStory?.Story?.mediaLink &&
        //     lastStory?.Story?.mediaType === "image") ||
        //   (lastStory?.mediaLink && lastStory?.mediaType === "image")
        // ) {
        //   const img = new Image();
        //   img.src = lastStory?.Story?.mediaLink ?? lastStory.mediaLink;
        // }
      }
    };

    preloadImages();
  }, [currentUserIndex, storyData, isOpen]);

  const navigate = (direction) => {
    if (direction === "next") {
      // Go to next user
      if (currentUserIndex < storyData.length - 1) {
        setCurrentUserIndex((prev) => prev + 1);
      }
    } else if (direction === "prev") {
      // Go to previous user
      if (currentUserIndex > 0) {
        setCurrentUserIndex((prev) => prev - 1);
      }
    }
  };

  const updateStoryLikeAndView = (payload = {}) => {
    api.sendRequest(likeStory, () => {}, payload, "");
  };

  // Update Story Like
  const updateStoryLike = (story) => {
    const payload = {
      storyId: story?.id,
      isLike: !story?.isLikedByCurrentUser,
    };
    // Like Story API Calls
    updateStoryLikeAndView(payload);
    setStories((prev) =>
      prev?.map((prevStories) => {
        // Storying the Prev Stories
        let storyData = {
          ...prevStories,
        };

        if (prevStories?.storyId === story?.id) {
          // Updating Current Story
          let updatedStory = {
            ...story,
            isLikedByCurrentUser: !story?.isLikedByCurrentUser,
            likeCount: story?.isLikedByCurrentUser
              ? +story?.likeCount - 1
              : +story?.likeCount + 1,
          };
          // Update Total Count
          const updatedCount = {
            ...prevStories?.storyCount,
            likeCount: story?.isLikedByCurrentUser
              ? prevStories?.storyCount?.likeCount - 1
              : prevStories?.storyCount?.likeCount + 1,
          };
          storyData = {
            ...storyData,
            seeMoreCollapsed: ({ toggleMore, action }) => (
              <>
                <StoryLikeButton
                  likeCount={updatedStory?.likeCount}
                  updateStoryLike={updateStoryLike}
                  isLikedByCurrentUser={updatedStory?.isLikedByCurrentUser}
                  story={updatedStory}
                  action={action}
                  isCreateByCurrentUser={
                    +loginUserData?.id === +updatedStory?.UserId
                  }
                  toggleMore={toggleMore}
                  storyCount={updatedCount ?? {}}
                  getStoryLikeUserList={getStoryLikeUserList}
                />
              </>
            ),
            storyCount: updatedCount,
          };
        }
        return storyData;
      })
    );
  };

  // Render Story Content
  const renderStoryContent = (story, header, currentUser) => {
    const { overlayImage, properties = [] } = story?.Story ?? story;

    return ({ action, story }) => (
      <Suspense>
        <WithSeeMore story={story} action={action}>
          <div className="tw-w-full tw-h-full">
            {header && (
              <CustomStoryHeader
                profileImage={header?.profileImage}
                heading={header?.heading}
                subheading={header?.subheading}
                currentUser={currentUser}
                onAction={handleHeaderAction}
                ele={{
                  id: currentUser?.id,
                  storyId: story?.Story?.id,
                }}
                action={action}
                setCurrentAction={setCurrentAction}
                isSettingVisible={isSettingVisible}
                router={router}
              />
            )}
            <div
              style={{
                width: "100%",
                height: "100%",
                position: "relative",
              }}
            >
              {/* Use Next.js Image for faster loading */}
              {overlayImage ? (
                <NextImage
                  src={overlayImage}
                  alt="Story background"
                  fill
                  className="tw-object-contain"
                  priority
                  sizes="100vw"
                  quality={85}
                  onError={(e) => {
                    // console.error("Failed to load overlay image:", overlayImage);
                    // Fallback to background image if NextImage fails
                    e.target.style.display = "none";
                    e.target.parentElement.style.backgroundImage = `url(${overlayImage})`;
                    e.target.parentElement.style.backgroundSize = "cover";
                    e.target.parentElement.style.backgroundPosition = "center";
                  }}
                />
              ) : (
                <div
                  style={{
                    width: "100%",
                    height: "100%",
                    backgroundColor: "#f0f0f0",
                  }}
                />
              )}
              <RenderProperties
                action={action}
                router={router}
                properties={properties}
              />
            </div>
          </div>
        </WithSeeMore>
      </Suspense>
    );
  };

  const currentUser = storyData[currentUserIndex];

  // Function to create stories from current user data
  const createStoriesFromData = (currentUser, dataKey, counts) => {
    return currentUser?.[dataKey]
      ?.map((story, i) => {
        const { mediaLink, mediaType, overlayImage } = story?.Story ?? story;
        // Safety check for required story data
        if (!story || (!mediaLink && !overlayImage)) {
          return null;
        }
        const userName = `${currentUser?.title ?? currentUser?.firstName} ${
          currentUser?.lastName ?? ""
        }`;
        const header = {
          heading: userName,
          profileImage: currentUser.image
            ? createProxyImageUrl(currentUser.image)
            : generateAvatarCanvas(userName, 100),
          subheading: getTimePassedFromNow(
            isHighlight
              ? currentUser?.createdAt
              : story?.Story?.createdAt ?? story?.createdAt
          ),
        };

        const storyId = story?.Story?.id ?? story?.id;

        const defaultStory = {};

        if (dataKey === "Stories") {
          defaultStory.seeMoreCollapsed = ({ toggleMore, action }) => (
            <>
              <StoryLikeButton
                likeCount={story?.likeCount}
                isLikedByCurrentUser={story?.isLikedByCurrentUser}
                updateStoryLike={updateStoryLike}
                story={story}
                action={action}
                isCreateByCurrentUser={+loginUserData?.id === +story?.UserId}
                toggleMore={toggleMore}
                storyCount={counts[storyId] ?? {}}
                getStoryLikeUserList={getStoryLikeUserList}
              />
            </>
          );
          defaultStory.seeMore = ({ close }) => (
            // <div
            //   style={{
            //     maxWidth: "100%",
            //     height: "100%",
            //     padding: 40,
            //     background: "white",
            //   }}
            // >
            //   <h2>Just checking the see more feature.</h2>
            //   <p style={{ textDecoration: "underline" }} onClick={close}>
            //     Go on, close this popup.
            //   </p>
            // </div>
            <>
              <SeeMoreDrawer
                story={story}
                close={close}
                storyCount={counts[storyId] ?? {}}
                isLikedByCurrentUser={story?.isLikedByCurrentUser}
                updateStoryLike={updateStoryLike}
                getStoryLikeUserList={getStoryLikeUserList}
                userList={userList}
                resetDataList={resetDataList}
                isLoading={api2.isLoading}
                pagination={pagination}
              />
            </>
          );
          defaultStory.storyCount = counts[storyId] ?? {};
        }

        if (mediaType === "video") {
          if (mediaLink.endsWith(".m3u8")) {
            const hlsStory = {
              content: ({ action, story }) => (
                <Suspense>
                  <WithSeeMore story={story} action={action}>
                    <HLSPlayer
                      overlayImage={overlayImage}
                      url={mediaLink}
                      action={action}
                      header={header}
                      customHeader={
                        <CustomStoryHeader
                          profileImage={header?.profileImage}
                          heading={header?.heading}
                          subheading={header?.subheading}
                          currentUser={currentUser}
                          onAction={handleHeaderAction}
                          ele={{
                            id: currentUser?.id,
                            storyId,
                          }}
                          router={router}
                          action={action}
                          setCurrentAction={setCurrentAction}
                          isSettingVisible={isSettingVisible}
                        />
                      }
                      properties={
                        <RenderProperties
                          action={action}
                          router={router}
                          properties={
                            story?.Story?.properties ?? story?.properties
                          }
                        />
                      }
                    />
                  </WithSeeMore>
                </Suspense>
              ),
              duration: story?.duration ? story?.duration * 1000 : 8000,
              header,
              storyId,
            };
            return { ...hlsStory, ...defaultStory };
          }
          const videoStory = {
            content: ({ action, story }) => (
              <Suspense>
                <WithSeeMore story={story} action={action}>
                  <VideoPlayer
                    overlayImage={overlayImage}
                    url={mediaLink}
                    action={action}
                    header={header}
                    customHeader={
                      <CustomStoryHeader
                        profileImage={header?.profileImage}
                        heading={header?.heading}
                        subheading={header?.subheading}
                        currentUser={currentUser}
                        onAction={handleHeaderAction}
                        ele={{
                          id: currentUser?.id,
                          storyId,
                        }}
                        action={action}
                        router={router}
                        setCurrentAction={setCurrentAction}
                        isSettingVisible={isSettingVisible}
                      />
                    }
                    properties={
                      <RenderProperties
                        action={action}
                        router={router}
                        properties={
                          story?.Story?.properties ?? story?.properties
                        }
                      />
                    }
                  />
                </WithSeeMore>
              </Suspense>
            ),
            duration: story?.duration ? +story?.duration * 1000 : 8000,
            header,
            storyId,
          };
          // For mp4/webm
          return { ...videoStory, ...defaultStory };
        }

        // likeCount isLikedByCurrentUser

        const imageStory = {
          content: renderStoryContent(story, header, currentUser),
          duration: story?.duration ? +story?.duration * 1000 : 8000,
          storyId,
        };

        return { ...imageStory, ...defaultStory };
      })
      .filter(Boolean); // Remove any null/undefined stories
  };

  // Update stories when currentUser or dataKey changes
  // useEffect(() => {
  //   const newStories = createStoriesFromData(currentUser, dataKey);
  //   setStories(newStories);
  // }, [currentUser, dataKey]);
  useEffect(() => {
    if (!currentUser) return;

    async function buildStoriesWithCounts() {
      const counts = {};
      if (dataKey === "Stories") {
        for (const story of currentUser?.[dataKey] ?? []) {
          if (+loginUserData?.id === +story?.UserId) {
            try {
              const res = await getStoryLikeAndViewCount({
                storyId: story.id,
              });
              counts[story.id] = res?.data;
            } catch (err) {
              console.error(err);
            }
          }
        }
      }

      // Only build stories after counts are fetched
      setStories(createStoriesFromData(currentUser, "Stories", counts));
    }

    buildStoriesWithCounts();
  }, [currentUser, loginUserData?.id, dataKey]);

  return (
    <div>
      <AddHighlightModal
        isOpen={isHighlighModalOpen}
        setIsOpen={setIsHighlighModalOpen}
        setRefresh={setReload}
        editData={editData}
      />
      <Modal
        open={isOpen}
        onClose={() => {
          setIsOpen(false);
        }}
        center
        classNames={{
          modal:
            "!tw-p-0 !tw-m-0 !tw-bg-black !tw-text-white !tw-w-full !tw-h-screen",
          closeButton: "!tw-z-[99999]",
        }}
        //   closeIcon={<X className="tw-text-white" />}
        closeIcon={
          <div className="tw-relative tw-top-[5rem] tw-right-[1.5rem] md:tw-static ">
            <CloseModalIcon size={30} />
          </div>
        }
        styles={{ modal: { maxWidth: "100vw", width: "100%", padding: 0 } }}
        showCloseIcon={true}
        focusTrapped={false}
      >
        <PopUpModal
          isLoading={api.isLoading}
          contentClassName="!tw-z-[99999]"
          isOpen={deleteData}
          confirmButtonText={stories?.length > 1 ? "Remove" : "Delete"}
          setIsOpen={(value) => {
            setDeleteData(value);
            // Resume story when modal is closed without confirming
            if (!value && currentAction && isPaused) {
              currentAction("play");
              setIsPaused(false);
            }
          }}
          mainMessage={
            stories?.length > 1
              ? "Remove this story?"
              : "Delete This Highlight?"
          }
          subMessage={`Are you sure you want to ${
            stories?.length > 1 ? "remove this story" : "delete this highlight"
          }? Once removed , it cannot be recovered.`}
          onConfirm={() => {
            const apiCall =
              stories?.length > 1 ? updateHighlights : deleteHighlights;
            let payload =
              stories?.length > 1
                ? {
                    id: deleteData?.id,
                    RemoveStoriesIds: [deleteData?.storyId],
                  }
                : {
                    id: deleteData?.id,
                  };
            // console.log(payload);
            api.sendRequest(
              apiCall,
              (res) => {
                // console.log(res);

                if (stories?.length > 1) {
                  safeToast.success("Highlight updated successfully");
                  // Remove the deleted story from the stories state
                  setStories((prevStories) =>
                    prevStories.filter(
                      (story) => story.storyId !== deleteData?.storyId
                    )
                  );
                } else {
                  safeToast.success(res?.message);
                  setReload((prev) => !prev);
                  setIsOpen(false);
                }

                // resetState();
                setDeleteData(null);
                setIsPaused(false);
                // Don't resume story after deletion - let it continue naturally
              },
              payload,
              ""
            );
          }}
        />
        <div className="tw-relative tw-w-full tw-h-screen tw-flex tw-items-center tw-justify-center">
          <Stories
            key={`user-${currentUserIndex}`}
            stories={stories}
            defaultInterval={6000}
            storyContainerStyles={{
              overflow: "hidden",
            }}
            keyboardNavigation
            height="95vh"
            onStoryStart={(_, value) => {
              // console.log(value, "Story is Open");
              updateStoryLikeAndView({
                storyId: value?.storyId,
              });
            }}
            onAllStoriesEnd={() => {
              if (currentUserIndex < storyData.length - 1) {
                setCurrentUserIndex((prev) => prev + 1);
              } else {
                setIsOpen(false);
              }
            }}
          />

          {/* Navigation buttons */}
          {/* {currentUserIndex > 0 && (
            <button
              onClick={() => navigate("prev")}
              className="tw-z-[9999] tw-absolute tw-left-[34.5%] tw-top-1/2 tw-rounded-full -tw-translate-y-1/2 tw-bg-[#4d4a48] tw-p-2 tw-text-white tw-text-center hover:tw-bg-[#5d5a58] tw-transition-colors"
            >
              <ChevronLeft size={25} />
            </button>
          )}
          {currentUserIndex < storyData.length - 1 && (
            <button
              onClick={() => navigate("next")}
              className="tw-z-[9999] tw-absolute tw-right-[34.5%] tw-top-1/2 tw-rounded-full -tw-translate-y-1/2 tw-bg-[#4d4a48] tw-p-2 tw-text-center tw-text-white hover:tw-bg-[#5d5a58] tw-transition-colors"
            >
              <ChevronRight size={25} />
            </button>
          )} */}
        </div>
      </Modal>
    </div>
  );
};

export default ModifyStoryPreview;

const StoryLikeButton = ({
  likeCount,
  isLikedByCurrentUser,
  updateStoryLike,
  story,
  action,
  isCreateByCurrentUser,
  storyCount,
  toggleMore,
  getStoryLikeUserList,
}) => {
  return isCreateByCurrentUser ? (
    <>
      <div className="tw-py-3 tw-px-5 tw-rounded-full tw-mx-9 tw-flex tw-justify-between tw-items-center tw-mb-3 tw-font-semibold tw-bg-white/15  tw-text-white">
        {/* See More Button */}
        <button
          type="button"
          onClick={() => {
            action("pause");
            getStoryLikeUserList(story, action);
            toggleMore("true");
          }}
          className="tw-flex tw-gap-2 tw-items-center"
        >
          <Eye /> <p className="">{formatNumber(storyCount?.viewCount ?? 0)}</p>
        </button>
        {/* Like Button */}
        <button
          type="button"
          className=""
          onClick={() => {
            updateStoryLike(story);
            action("pause");
          }}
        >
          <div className="tw-flex tw-justify-center tw-gap-2 tw-items-center tw-py-2 tw-px-3 tw-bg-white/15 tw-text-white tw-rounded-full  ">
            {isLikedByCurrentUser ? (
              <HeartFillOutIcon size={20} />
            ) : (
              <HeartIcon size={20} stroke="#fff" />
            )}
            <p className="tw-font-semibold tw-text-lg">
              {formatNumber(storyCount?.likeCount ?? 0)}
            </p>
          </div>
        </button>
      </div>
    </>
  ) : (
    <button
      type="button"
      className="tw-flex tw-justify-center tw-mb-3 tw-mx-auto"
      onClick={() => {
        updateStoryLike(story);
        action("pause");
      }}
    >
      <div className="tw-flex tw-justify-center tw-gap-2 tw-items-center tw-p-3 tw-bg-white/15 tw-text-white tw-rounded-full  ">
        {isLikedByCurrentUser ? (
          <HeartFillOutIcon />
        ) : (
          <HeartIcon stroke="#fff" />
        )}
        <p className="">{likeCount}</p>
      </div>
    </button>
  );
};

const SeeMoreDrawer = ({
  close,
  story,
  storyCount,
  isLikedByCurrentUser,
  updateStoryLike,
  userList,
  resetDataList,
  pagination,
  setPagination,
  action,
  isLoading,
  getStoryLikeUserList,
}) => {
  return (
    <>
      <div className="tw-h-[80%] tw-absolute tw-bottom-0 tw-rounded-t-3xl tw-w-full  tw-max-w-full tw-p-5 tw-text-black tw-bg-white">
        <div className="tw-flex tw-gap-3 tw-items-center tw-justify-between">
          <div className="tw-w-full tw-py-3 tw-px-5 tw-rounded-full  tw-flex tw-justify-between tw-items-center  tw-font-semibold  tw-bg-[#787E8926] ">
            {/* See More Button */}
            <div
              onClick={() => {}}
              className="tw-flex tw-gap-2 tw-items-center"
            >
              <Eye stroke="#111" />
              <p className="">{formatNumber(storyCount?.viewCount ?? 0)}</p>
            </div>
            {/* Like Button */}
            <button
              type="button"
              className=""
              onClick={() => {
                updateStoryLike(story);
                action("pause");
              }}
            >
              <div className="tw-flex tw-justify-center tw-gap-2 tw-items-center tw-py-2 tw-px-3  tw-rounded-full  ">
                {isLikedByCurrentUser ? (
                  <HeartFillOutIcon size={20} />
                ) : (
                  <HeartIcon size={20} stroke="#111" />
                )}
                <p className="tw-font-semibold tw-text-lg">
                  {formatNumber(storyCount?.likeCount ?? 0)}
                </p>
              </div>
            </button>
          </div>
          <button
            type="button"
            onClick={() => {
              close();
              resetDataList();
            }}
          >
            <CloseModalIcon size={30} />
          </button>
        </div>
        {/* User Data */}
        <div className="tw-h-[89%]  tw-overflow-auto tw-mt-5">
          {isLoading ? (
            <>
              <SelectProjectSkeleton count={7} />
            </>
          ) : (
            <div id="scrollableDiv" className="tw-h-[22rem]">
              <InfiniteScroll
                dataLength={userList?.length ?? 0}
                scrollableTarget="scrollableDiv"
                className="infinite-scrollbar tw-px-1"
                hasMore={
                  pagination?.page <
                  Math.ceil(pagination?.total / pagination?.limit)
                  // true
                }
                next={() => {
                  let tempPage = pagination?.page + 1;
                  setPagination((prev) => ({
                    ...prev,
                    page: prev?.page + 1,
                  }));

                  getStoryLikeUserList(story, action, tempPage);
                }}
                loader={<SelectProjectSkeleton count={3} />}
              >
                {userList?.map((ele) => (
                  <div
                    key={ele?.id}
                    className="tw-flex tw-gap-3 tw-items-center tw-font-medium tw-text-xl tw-mb-3"
                  >
                    <div className="tw-relative">
                      <UserAvatar
                        imageUrl={ele?.User?.image}
                        userName={ele?.User?.firstName}
                      />
                      {ele?.isLike && (
                        <div className="tw-absolute tw-bottom-0 tw-right-0">
                          <StoryLikeIcon />
                        </div>
                      )}
                    </div>
                    {/* Name */}
                    <p className="">
                      {ele?.User?.firstName + ele?.User?.lastName}
                    </p>
                  </div>
                ))}
              </InfiniteScroll>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
