'use client'
import { role } from "@/utils/function";
import dayjs from "dayjs";
import React, { createContext, useState } from "react";
export const valContext = createContext();

const ValProvider = ({ children }) => {
  const [isAlternativePopup, setIsAlternativePopup] = useState(false);
  const [latestCreatedPost, setLatestCreatedPost] = useState(null);
  const [forgotEmail, setForgotEmail] = useState("");
  const [updatedProfile, setUpdatedProfile] = useState(null)
  const [isNewNotifications, setIsNewNotifications] = useState(false)
  const [globalSearch, setGlobalSearch] = useState("");
  const [totalSearchRecords, setTotalSearchRecords] = useState(0)
  const [searchData, setSearchData] = useState([]);
  const [searchPagination, setSearchPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  })
  const [isMobile, setIsMobile] = useState(false);
  const [subProjectPostCount, setSubProjectPostCount] = useState(0);
  const [loginUserData, setLoginUserData] = useState(null);
  const [chatList, setChatList] = useState([
    {
      role: role.assistant,
      content: <p className="tw-font-semibold">Hi, I'm Flowie. How can I help you build your Project journey?</p>,
      timestamp: dayjs().toISOString(),
    },
  ]);
  const [firstTimeLoading, setFirstTimeLoading] = useState(true);
  return (
    <valContext.Provider
      value={{
        latestCreatedPost,
        setLatestCreatedPost,
        isAlternativePopup,
        setIsAlternativePopup,
        forgotEmail,
        setForgotEmail,
        updatedProfile,
        setUpdatedProfile,
        isNewNotifications,
        setIsNewNotifications,
        globalSearch,
        setGlobalSearch,
        totalSearchRecords,
        setTotalSearchRecords,
        searchData,
        setSearchData,
        searchPagination,
        setSearchPagination,
        isMobile,
        setIsMobile,
        subProjectPostCount,
        setSubProjectPostCount,
        loginUserData,
        setLoginUserData,
        chatList,
        setChatList,
        firstTimeLoading,
        setFirstTimeLoading,

      }}>
      {children}
    </valContext.Provider>
  );
};

export default ValProvider;